import { TableColumnConfig } from '@medly-components/core';

export const MyGoalsCols: TableColumnConfig[] = [
    {
        field: 'id',
        title: 'ID'
    },
    {
        field: 'goalDescription',
        title: 'Description',
        fraction: 2
    },
    {
        field: 'goalType',
        title: 'Type'
    },
    {
        field: 'owner',
        title: 'Owner'
    },
    {
        field: 'createdBy',
        title: 'Created By'
    },
    {
        field: 'status',
        title: 'Status'
    },
    {
        field: 'action',
        title: 'Action'
    }
];

export const TeamGoalsCols: TableColumnConfig[] = [
    {
        field: 'id',
        title: 'ID'
    },
    {
        field: 'goalDescription',
        title: 'Description',
        fraction: 2
    },
    {
        field: 'goalType',
        title: 'Type'
    },
    {
        field: 'owner',
        title: 'Owner'
    },
    {
        field: 'createdBy',
        title: 'Created By'
    },
    {
        field: 'status',
        title: 'Status'
    },
    {
        field: 'action',
        title: 'Action'
    }
];
