import { useState } from 'react';

export const useGoals = () => {
    const [activeTab, setActiveTab] = useState('myGoals');
    const [page, setPage] = useState(1);
    const [goalStatusFilter, setGoalStatusFilter] = useState('');
    const [goalTypeFilter, setGoalTypeFilter] = useState('');
    const [ownerSearch, setOwnerSearch] = useState('');

    const handleTabChange = (id: string) => {
        setActiveTab(id);
    };

    const handlePageChange = (page: number) => {
        setPage(page);
    };

    const addGoalPage = () => {
        // TODO: Implement navigation to add goal page
        console.log('Navigate to add goal page');
    };

    const handleGoalStatusChange = (value: string) => {
        setGoalStatusFilter(value);
    };

    const handleGoalTypeChange = (value: string) => {
        setGoalTypeFilter(value);
    };

    const handleOwnerSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setOwnerSearch(event.target.value);
    };

    const myGoalsData = [
        {
            id: 'G001',
            goalDescription: 'Learn TypeScript in depth',
            goalType: 'Individual',
            owner: 'Current User',
            createdBy: 'Admin',
            status: 'In Progress',
            action: 'View'
        },
        {
            id: 'G002',
            goalDescription: 'Build a full-stack React application',
            goalType: 'Individual',
            owner: 'Current User',
            createdBy: 'Admin',
            status: 'Completed',
            action: 'View'
        }
    ];

    const teamGoalsData = [
        {
            id: 'T001',
            goalDescription: 'Improve API performance by 20%',
            goalType: 'Team',
            owner: 'John Doe',
            createdBy: 'Manager',
            status: 'In Progress',
            action: 'View'
        },
        {
            id: 'T002',
            goalDescription: 'Refactor legacy authentication module',
            goalType: 'Team',
            owner: 'Jane Smith',
            createdBy: 'Manager',
            status: 'To Do',
            action: 'View'
        }
    ];

    const goalsData = activeTab === 'myGoals' ? myGoalsData : teamGoalsData;

    return {
        activeTab,
        handleTabChange,
        goalsData,
        page,
        handlePageChange,
        totalGoalsDataCount: goalsData.length,
        goalsDataIsLoading: false,
        // TODO: Implement handleSortChange
        addGoalPage,
        goalStatusFilter,
        goalTypeFilter,
        ownerSearch,
        handleGoalStatusChange,
        handleGoalTypeChange,
        handleOwnerSearchChange
    };
};
