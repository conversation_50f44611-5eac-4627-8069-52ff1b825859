import { Chip, Text } from '@medly-components/core';
import styled from 'styled-components';

export const StyledChip = styled(Chip)<{ label: string }>`
    border: white;
    margin: 0;
    padding: 0.5rem;
    min-width: 100px;
    ${Text.Style} {
        font-size: 1.1rem !important;
        color: #fff;
    }
    background-color: ${({ theme, label }) => {
        switch (label) {
            case 'Completed':
                return theme.customColors.positive;
            case 'In Progress':
                return theme.customColors.improvement;
            case 'To Do':
                return theme.customColors.ToDoColor;
            case 'Deferred':
                return theme.customColors.unacceptable;
            default:
                return theme.colors.grey[500];
        }
    }};
`;
