import { PageContent } from '@components';
import { CustomTable } from '@components/reusableComponents/CustomTable/CustomTable';
import ListHeader from '@components/reusableComponents/ListHeader';
import { Tabs, TextField } from '@medly-components/core';
import { PersonIcon, GroupIcon } from '@medly-components/icons';
import { MyGoalsCols, TeamGoalsCols } from './columns';
import { StyledTableWrapper, StyledTabs, FilterContainer, HeaderContainer } from './Goals.styled';
import { useGoals } from './useGoals';
import { StyledSingleSelect } from '@common';

export const Goals = () => {
    const {
        handleTabChange,
        goalsData,
        totalGoalsDataCount,
        handlePageChange,
        page,
        activeTab,
        goalsDataIsLoading,
        addGoalPage,
        goalStatusFilter,
        goalTypeFilter,
        ownerSearch,
        handleGoalStatusChange,
        handleGoalTypeChange,
        handleOwnerSearchChange
    } = useGoals();

    const goalStatusOptions = [
        { value: 'all', label: 'All' },
        { value: 'inProgress', label: 'In Progress' },
        { value: 'completed', label: 'Completed' },
        { value: 'toDo', label: 'To Do' }
    ];

    const goalTypeOptions = [
        { value: 'all', label: 'All' },
        { value: 'individual', label: 'Individual' },
        { value: 'team', label: 'Team' }
    ];

    return (
        <PageContent>
            <ListHeader title="Goals" actionButtonLabel="Add Goal" actionButtonClick={addGoalPage} />

            <HeaderContainer>
                <StyledTabs aria-label="Goals tabs" tabSize="M" variant="outlined" onChange={id => handleTabChange(id)}>
                    <Tabs.Tab active={activeTab === 'myGoals'} id="myGoals" label="My Goals" icon={PersonIcon} />
                    <Tabs.Tab active={activeTab === 'teamGoals'} id="teamGoals" label="Team Goals" icon={GroupIcon} />
                </StyledTabs>

                <FilterContainer>
                    <StyledSingleSelect
                        options={goalStatusOptions}
                        variant="outlined"
                        placeholder="Select Status"
                        label="Goal Status"
                        onChange={val => val && handleGoalStatusChange(val)}
                        value={goalStatusFilter}
                        size="M"
                        minWidth="20rem"
                    />
                    <StyledSingleSelect
                        options={goalTypeOptions}
                        variant="outlined"
                        placeholder="Select Type"
                        label="Goal Type"
                        onChange={val => val && handleGoalTypeChange(val)}
                        value={goalTypeFilter}
                        size="M"
                        minWidth="20rem"
                    />
                    {activeTab === 'teamGoals' && (
                        <TextField
                            variant="outlined"
                            // placeholder="Search by owner"
                            label="Search by Owner"
                            value={ownerSearch}
                            onChange={handleOwnerSearchChange}
                            size="M"
                            minWidth="20rem"
                        />
                    )}
                </FilterContainer>
            </HeaderContainer>

            <StyledTableWrapper>
                <CustomTable
                    data={goalsData}
                    tableKey={activeTab}
                    columns={activeTab === 'myGoals' ? MyGoalsCols : TeamGoalsCols}
                    isLoading={goalsDataIsLoading}
                    activePage={page || 1}
                    count={totalGoalsDataCount || 0}
                    handlePageChange={handlePageChange}
                />
            </StyledTableWrapper>
        </PageContent>
    );
};
