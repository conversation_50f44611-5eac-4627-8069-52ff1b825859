import { ParentStyledTabs, StyledModal } from '@common';
import { Text } from '@medly-components/core';
import styled from 'styled-components';

export const StyledTabs = styled(ParentStyledTabs)`
    button {
        width: 21rem;
    }
    div[role='tabpanel'] {
        display: none;
    }

    button[aria-selected='false'] div div span {
        color: #666;
    }

    button[aria-selected='false'] svg path {
        fill: #666;
    }

    button[aria-selected='false'] div div span:nth-of-type(2) {
        color: #666;
        background-color: ${({ theme }) => theme.colors.grey[200]};
    }

    button[aria-selected='false']:hover div div span:nth-of-type(2) {
        background-color: ${({ theme }) => theme.colors.grey[200]};
    }
`;

export const StyledTableWrapper = styled('div')`
    table {
        margin-top: 0;
        border-radius: 0 0.8rem 0.8rem 0.8rem;
    }
`;

export const StyledPopup = styled(StyledModal)`
    #medly-modal-popup {
        overflow-y: auto !important;
        width: 103.6rem;
    }
    #medly-modal-inner-container #medly-modal-content {
        padding-bottom: 3rem;
    }
`;

export const GoalLayer = styled.div`
    display: flex;
    flex-direction: column;
`;

export const GoalDescriptionLayer = styled.div`
    display: flex;
    flex-direction: column;
    row-gap: 1rem;

    span > ul,
    p,
    ol {
        margin-top: 0;
        margin-bottom: 0;
    }
`;

export const StyledHeading = styled(Text)`
    font-size: 1.3rem;
`;

export const StyledText = styled(Text)`
    font-size: 1.3rem;
`;

export const StyledHTMLText = styled(Text)`
    width: 100%;
    p {
        word-wrap: break-word;
        margin-block-start: 0;
        font-size: 1.3rem;
    }
`;

export const HeaderContainer = styled.div`
    display: flex;
    justify-content: space-between;
    align-items: center;
`;

export const FilterContainer = styled.div`
    display: flex;
    align-items: center;
    gap: 2rem;
`;
